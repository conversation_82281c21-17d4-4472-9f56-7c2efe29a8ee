import 'package:flutter/material.dart';
import '../../action_history.dart';
import 'header_component.dart';
import 'body_component.dart';
import 'footer_component.dart';

class ActionLogModal extends StatefulWidget {
  final HistoryPanelState panelState;
  final VoidCallback onPanelToggle;
  final List<String> handHistory;
  final ScrollController historyScrollController;
  final VoidCallback onCopyHistory;
  final VoidCallback? onSwapCardView;
  final VoidCallback? onExitTable;

  ActionLogModal({
    Key? key,
    required this.panelState,
    required this.onPanelToggle,
    required this.handHistory,
    required this.historyScrollController,
    required this.onCopyHistory,
    this.onSwapCardView,
    this.onExitTable,
  }) : super(key: key);

  @override
  _ActionLogModalState createState() => _ActionLogModalState();
}

class _ActionLogModalState extends State<ActionLogModal> {
  bool action_log_opened = false;

  void toggleActionLog() {
    setState(() {
      // Predict the next state based on current state (following the cycle: closed -> semiOpen -> fullyOpen -> closed)
      HistoryPanelState nextState;
      switch (widget.panelState) {
        case HistoryPanelState.closed:
          nextState = HistoryPanelState.semiOpen;
          break;
        case HistoryPanelState.semiOpen:
          nextState = HistoryPanelState.fullyOpen;
          break;
        case HistoryPanelState.fullyOpen:
          nextState = HistoryPanelState.closed;
          break;
      }

      // Set action_log_opened based on the next state
      action_log_opened = nextState != HistoryPanelState.closed;
    });

    // Call the parent's toggle function after updating our local state
    widget.onPanelToggle();
  }

  double _getHeightForState() {
    if (!action_log_opened) {
      return 100; // Closed state
    }

    switch (widget.panelState) {
      case HistoryPanelState.closed:
        return 100;
      case HistoryPanelState.semiOpen:
        return 320; // Medium height for semi-open (increased to prevent overflow)
      case HistoryPanelState.fullyOpen:
        return 700; // Full height for fully open
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      width: 302,
      margin: EdgeInsets.only(bottom: 10),
      height: _getHeightForState(),
      padding: EdgeInsets.only(left: 16, right:16, top: 2, bottom:8),
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xCC000000), width: 2.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: InkWell(
              child: Icon(
                Icons.menu,
                color: Colors.black,
                size: 12,
              ),
            ),
          ),
          const SizedBox(height: 8),
          HeaderComponent(
            onMenuTap: toggleActionLog,
            actionLogOpened: action_log_opened,
          ),
          if (action_log_opened) ...[
            Expanded(
              child:Container(
                padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Color.fromARGB(255, 0, 39, 41),
        borderRadius: BorderRadius.circular(10),
      ),                
                 child:
              Column(
                
                children: [
                 
                  Expanded(
                    child:  AnimatedSwitcher(
                        duration: const Duration(milliseconds: 500),
                        child: _buildContentForState(widget.panelState, isMobile),
                      ),
                    ),

                   const SizedBox(height: 4),
                  // const FooterComponent(),
                  // const SizedBox(height: 8),
                ],
              )),
            ),
          ] else ...[
            const SizedBox(height: 8),
          ],
        ],
      ),
    );
  }

  Widget _buildContentForState(HistoryPanelState state, bool isMobile) {
    switch (state) {
      case HistoryPanelState.closed:
        return const SizedBox.shrink();
      case HistoryPanelState.semiOpen:
        return _buildSemiOpenHistory(isMobile);
      case HistoryPanelState.fullyOpen:
        return _buildFullyOpenHistory();
    }
  }

  Widget _buildSemiOpenHistory(bool isMobile) {
    final displayed = takeLast(widget.handHistory, isMobile ? 2 : 5);
    return Container(
      clipBehavior: Clip.none,
      //padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
      decoration: BoxDecoration(
        color: Color.fromARGB(255, 0, 39, 41),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: List.generate(displayed.length, (index) {
          final isLast = index == displayed.length - 1;
          return Container(
            decoration: BoxDecoration(
              borderRadius: isLast
                  ? const BorderRadius.only(
                      bottomLeft: Radius.circular(6),
                      bottomRight: Radius.circular(6),
                    )
                  : null,
            ),
           
            child: Row(
              children: [
                Flexible(
                  flex: 1,
                  child: Stack(children: [
                    Text(
                      displayed[index].startsWith('Player')
                          ? displayed[index].substring(0, 8)
                          : displayed[index],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w900,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 4
                          ..color = Colors.black,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      displayed[index].startsWith('Player')
                          ? displayed[index].substring(0, 8)
                          : displayed[index],
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ]),
                ),
                SizedBox(
                  width: 2,
                ),
                if (displayed[index].startsWith('Player') &&
                    displayed[index].length > 8)
                  Flexible(
                    flex: 2,
                    child: Stack(
                      children: [
                        Text(
                          displayed[index].substring(8),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 4
                              ..color = Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          displayed[index].substring(8),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            color: Color.fromARGB(255, 112, 112, 112),
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildFullyOpenHistory() {
    final displayed = takeLast(widget.handHistory, !(widget.handHistory.length > 15) ? 15 : widget.handHistory.length);
    return Scrollbar(
      thumbVisibility: true,
      controller: widget.historyScrollController,
      child: ListView.builder(
        controller: widget.historyScrollController,
        itemCount: widget.handHistory.length,
        itemBuilder: (context, index) {
 final isLast = index == displayed.length - 1;
          return Container(
            decoration: BoxDecoration(
              borderRadius: isLast
                  ? const BorderRadius.only(
                      bottomLeft: Radius.circular(6),
                      bottomRight: Radius.circular(6),
                    )
                  : null,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
            child: Row(
              children: [
                
                Flexible(
                  flex: 1,
                  child: Stack(children: [
                    Text(
                      displayed[index].startsWith('Player')
                          ? displayed[index].substring(0, 8)
                          : displayed[index],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w900,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 4
                          ..color = Colors.black,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      displayed[index].startsWith('Player')
                          ? displayed[index].substring(0, 8)
                          : displayed[index],
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ]),
                ),
                SizedBox(
                  width: 2,
                ),
                if (displayed[index].startsWith('Player') &&
                    displayed[index].length > 8)
                  Flexible(
                    flex: 2,
                    child: Stack(
                      children: [
                        Text(
                          displayed[index].substring(8),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 4
                              ..color = Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          displayed[index].substring(8),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            color: Color.fromARGB(255, 112, 112, 112),
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<String> takeLast(List<String> list, int n) {
    if (n <= 0) return [];
    if (n >= list.length) return list;
    return list.sublist(list.length - n);
  }
}
